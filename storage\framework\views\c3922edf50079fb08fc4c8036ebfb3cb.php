<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'Dashboard'); ?> - Umer Farooque Portfolio</title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'Admin Dashboard for Umer Farooque Portfolio Management'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Dashboard Specific CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard/main.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard/components.css')); ?>">
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="dashboard-body">
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php echo $__env->make('dashboard.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        
        <!-- Main Content Area -->
        <div class="dashboard-main">
            <!-- Header -->
            <?php echo $__env->make('dashboard.partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            
            <!-- Content -->
            <main class="dashboard-content">
                <?php if(session('success')): ?>
                    <div class="dashboard-alert dashboard-alert-success">
                        <div class="dashboard-alert-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="dashboard-alert-content">
                            <div class="dashboard-alert-message"><?php echo e(session('success')); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if(session('error')): ?>
                    <div class="dashboard-alert dashboard-alert-danger">
                        <div class="dashboard-alert-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="dashboard-alert-content">
                            <div class="dashboard-alert-message"><?php echo e(session('error')); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if($errors->any()): ?>
                    <div class="dashboard-alert dashboard-alert-danger">
                        <div class="dashboard-alert-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="dashboard-alert-content">
                            <div class="dashboard-alert-title">Please fix the following errors:</div>
                            <ul class="dashboard-alert-message" style="margin-bottom: 0; padding-left: 1.25rem;">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>
    
    <!-- Mobile Overlay -->
    <div class="dashboard-mobile-overlay" id="mobileOverlay"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    
    <!-- Dashboard JS -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.querySelector('.dashboard-sidebar');
            const overlay = document.getElementById('mobileOverlay');
            
            if (mobileToggle) {
                mobileToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                    overlay.classList.toggle('open');
                });
            }
            
            if (overlay) {
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('open');
                    overlay.classList.remove('open');
                });
            }
            
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.dashboard-alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
            
            // Confirm delete actions
            const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const message = this.getAttribute('data-confirm-delete') || 'Are you sure you want to delete this item?';
                    if (!confirm(message)) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\laravel-porfolio\resources\views/dashboard/layout.blade.php ENDPATH**/ ?>