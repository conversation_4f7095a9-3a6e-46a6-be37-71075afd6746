<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Project;

class HomeController extends Controller
{
    public function index()
    {
        // Temporary: Empty projects array for testing without database
        $projects = collect([]);
        return view('website.home', compact('projects'));
    }

    public function about()
    {
        return view('website.about');
    }

    public function projects()
    {
        // Temporary: Empty projects array for testing without database
        $projects = collect([]);
        return view('website.projects', compact('projects'));
    }
}
