@extends('layouts.portfolio')

@section('title', '<PERSON><PERSON> - Full Stack Developer')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Hi, I'm <PERSON><PERSON></h1>
                <h2 class="h3 mb-4">Full Stack Developer</h2>
                <p class="lead mb-4">
                    I specialize in Laravel, WordPress, PHP, JavaScript, jQuery, Bootstrap, REST APIs, and GitHub. 
                    I create robust web applications and deliver exceptional user experiences.
                </p>
                <div class="d-flex gap-3">
                    <a href="{{ route('projects') }}" class="btn btn-light btn-lg">View My Work</a>
                    <a href="{{ route('download.cv') }}" class="btn btn-success btn-lg">
                        <i class="fas fa-download"></i> Download CV
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">Get In Touch</a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="bg-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 300px; height: 300px;">
                    <i class="fas fa-code fa-8x text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">My Skills</h2>
                <p class="lead">Technologies I work with</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-laravel fa-3x text-danger mb-3"></i>
                        <h5>Laravel</h5>
                        <p>Expert in Laravel framework for building robust web applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-wordpress fa-3x text-primary mb-3"></i>
                        <h5>WordPress</h5>
                        <p>Custom WordPress development and theme customization</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-js-square fa-3x text-warning mb-3"></i>
                        <h5>JavaScript</h5>
                        <p>Modern JavaScript, jQuery, and frontend frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-php fa-3x text-info mb-3"></i>
                        <h5>PHP</h5>
                        <p>Server-side development with PHP and modern frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-bootstrap fa-3x text-purple mb-3"></i>
                        <h5>Bootstrap</h5>
                        <p>Responsive design with Bootstrap and modern CSS</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-github fa-3x text-dark mb-3"></i>
                        <h5>GitHub</h5>
                        <p>Version control and collaborative development</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Projects Section -->
@if($projects->count() > 0)
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Recent Projects</h2>
                <p class="lead">Some of my latest work</p>
            </div>
        </div>
        <div class="row g-4">
            @foreach($projects as $project)
            <div class="col-md-4">
                <div class="card project-card h-100 border-0 shadow-sm">
                    @if($project->image_path)
                        <img src="{{ asset('storage/' . $project->image_path) }}" class="card-img-top" alt="{{ $project->title }}" style="height: 200px; object-fit: cover;">
                    @else
                        <div class="bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-code fa-3x text-white"></i>
                        </div>
                    @endif
                    <div class="card-body">
                        <h5 class="card-title">{{ $project->title }}</h5>
                        <p class="card-text">{{ Str::limit($project->description, 100) }}</p>
                        <p class="text-muted small">
                            <strong>Tech:</strong> {{ $project->tech_stack }}
                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        @if($project->github_url)
                            <a href="{{ $project->github_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fab fa-github"></i> View Code
                            </a>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="text-center mt-5">
            <a href="{{ route('projects') }}" class="btn btn-primary btn-lg">View All Projects</a>
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container text-center">
        <h2 class="display-5 fw-bold mb-4">Ready to Work Together?</h2>
        <p class="lead mb-4">Let's discuss your next project and bring your ideas to life.</p>
        <a href="{{ route('contact') }}" class="btn btn-light btn-lg">Get In Touch</a>
    </div>
</section>
@endsection
