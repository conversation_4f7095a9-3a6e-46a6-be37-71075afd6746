<?php $__env->startSection('title', 'Umer <PERSON> - Full Stack Developer'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Hi, I'm <PERSON><PERSON></h1>
                <h2 class="h3 mb-4">Full Stack Developer</h2>
                <p class="lead mb-4">
                    I specialize in Laravel, WordPress, PHP, JavaScript, jQuery, Bootstrap, REST APIs, and GitHub. 
                    I create robust web applications and deliver exceptional user experiences.
                </p>
                <div class="d-flex gap-3">
                    <a href="<?php echo e(route('projects')); ?>" class="btn btn-light btn-lg">View My Work</a>
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-light btn-lg">Get In Touch</a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="bg-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 300px; height: 300px;">
                    <i class="fas fa-code fa-8x text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">My Skills</h2>
                <p class="lead">Technologies I work with</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-laravel fa-3x text-danger mb-3"></i>
                        <h5>Laravel</h5>
                        <p>Expert in Laravel framework for building robust web applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-wordpress fa-3x text-primary mb-3"></i>
                        <h5>WordPress</h5>
                        <p>Custom WordPress development and theme customization</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-js-square fa-3x text-warning mb-3"></i>
                        <h5>JavaScript</h5>
                        <p>Modern JavaScript, jQuery, and frontend frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-php fa-3x text-info mb-3"></i>
                        <h5>PHP</h5>
                        <p>Server-side development with PHP and modern frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-bootstrap fa-3x text-purple mb-3"></i>
                        <h5>Bootstrap</h5>
                        <p>Responsive design with Bootstrap and modern CSS</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-github fa-3x text-dark mb-3"></i>
                        <h5>GitHub</h5>
                        <p>Version control and collaborative development</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Projects Section -->
<?php if($projects->count() > 0): ?>
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Recent Projects</h2>
                <p class="lead">Some of my latest work</p>
            </div>
        </div>
        <div class="row g-4">
            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-4">
                <div class="card project-card h-100 border-0 shadow-sm">
                    <?php if($project->screenshot_url): ?>
                        <img src="<?php echo e($project->screenshot_url); ?>" class="card-img-top" alt="<?php echo e($project->title); ?>" style="height: 200px; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-code fa-3x text-white"></i>
                        </div>
                    <?php endif; ?>
                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($project->title); ?></h5>
                        <p class="card-text"><?php echo e(Str::limit($project->description, 100)); ?></p>
                        <p class="text-muted small">
                            <strong>Tech:</strong> <?php echo e($project->tech_stack); ?>

                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <?php if($project->github_url): ?>
                            <a href="<?php echo e($project->github_url); ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fab fa-github"></i> View Code
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="text-center mt-5">
            <a href="<?php echo e(route('projects')); ?>" class="btn btn-primary btn-lg">View All Projects</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container text-center">
        <h2 class="display-5 fw-bold mb-4">Ready to Work Together?</h2>
        <p class="lead mb-4">Let's discuss your next project and bring your ideas to life.</p>
        <a href="<?php echo e(route('contact')); ?>" class="btn btn-light btn-lg">Get In Touch</a>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.portfolio', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel-porfolio\resources\views/home.blade.php ENDPATH**/ ?>