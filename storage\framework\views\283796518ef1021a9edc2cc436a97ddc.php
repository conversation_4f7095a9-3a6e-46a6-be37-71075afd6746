<?php $__env->startSection('title', 'Umer <PERSON>que - Full Stack Developer'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4 hero-title">Hi, I'm <span class="text-warning hero-name">Umer Farooque</span></h1>
                <h2 class="h3 mb-4 hero-subtitle">Full Stack Developer</h2>
                <p class="lead mb-4 hero-description">
                    I specialize in Laravel, WordPress, PHP, JavaScript, jQuery, Bootstrap, REST APIs, and GitHub.
                    I create robust web applications and deliver exceptional user experiences.
                </p>
                <div class="d-flex gap-3 hero-buttons">
                    <a href="<?php echo e(route('projects')); ?>" class="btn btn-light btn-lg hero-btn-1">
                        <i class="fas fa-code me-2"></i>View My Work
                    </a>
                    <a href="<?php echo e(route('download.cv')); ?>" class="btn btn-success btn-lg hero-btn-2">
                        <i class="fas fa-download me-2"></i> Download CV
                    </a>
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-light btn-lg hero-btn-3">
                        <i class="fas fa-envelope me-2"></i>Get In Touch
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="bg-white rounded-circle d-inline-flex align-items-center justify-content-center floating hero-icon-container" style="width: 300px; height: 300px;">
                    <i class="fas fa-code fa-8x text-primary hero-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Animated background elements -->
    <div class="hero-bg-elements">
        <div class="bg-element bg-element-1"></div>
        <div class="bg-element bg-element-2"></div>
        <div class="bg-element bg-element-3"></div>
    </div>
</section>

<!-- Skills Section -->
<section class="section-padding bg-light" data-aos="fade-up">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold section-title" data-aos="fade-up" data-aos-delay="100">My Skills</h2>
                <p class="lead section-subtitle" data-aos="fade-up" data-aos-delay="200">Technologies I work with</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-4" data-aos="zoom-in" data-aos-delay="100">
                <div class="card h-100 text-center border-0 shadow-sm skill-card">
                    <div class="card-body">
                        <i class="fab fa-laravel fa-3x text-danger mb-3"></i>
                        <h5>Laravel</h5>
                        <p>Expert in Laravel framework for building robust web applications</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4" data-aos="zoom-in" data-aos-delay="200">
                <div class="card h-100 text-center border-0 shadow-sm skill-card">
                    <div class="card-body">
                        <i class="fab fa-wordpress fa-3x text-primary mb-3"></i>
                        <h5>WordPress</h5>
                        <p>Custom WordPress development and theme customization</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4" data-aos="zoom-in" data-aos-delay="300">
                <div class="card h-100 text-center border-0 shadow-sm skill-card">
                    <div class="card-body">
                        <i class="fab fa-js-square fa-3x text-warning mb-3"></i>
                        <h5>JavaScript</h5>
                        <p>Modern JavaScript, jQuery, and frontend frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-php fa-3x text-info mb-3"></i>
                        <h5>PHP</h5>
                        <p>Server-side development with PHP and modern frameworks</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-bootstrap fa-3x text-purple mb-3"></i>
                        <h5>Bootstrap</h5>
                        <p>Responsive design with Bootstrap and modern CSS</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fab fa-github fa-3x text-dark mb-3"></i>
                        <h5>GitHub</h5>
                        <p>Version control and collaborative development</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Projects Section -->
<?php if($projects->count() > 0): ?>
<section class="section-padding" data-aos="fade-up">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold section-title" data-aos="fade-up" data-aos-delay="100">Recent Projects</h2>
                <p class="lead section-subtitle" data-aos="fade-up" data-aos-delay="200">Some of my latest work</p>
            </div>
        </div>
        <div class="row g-4">
            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-4" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                <div class="card project-card h-100 border-0 shadow-sm">
                    <?php if($project->images->count() > 0 || $project->image_path): ?>
                        <div class="project-image-container">
                            <?php if($project->images->count() > 1): ?>
                                <!-- Swiper for multiple images -->
                                <div class="swiper project-swiper project-swiper-home-<?php echo e($loop->index); ?>">
                                    <div class="swiper-wrapper">
                                        <?php $__currentLoopData = $project->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="swiper-slide">
                                                <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                                     class="card-img-top" alt="<?php echo e($project->title); ?>"
                                                     style="height: 200px; object-fit: cover;">
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-button-prev"></div>
                                </div>
                            <?php else: ?>
                                <!-- Single image -->
                                <?php
                                    $imagePath = $project->images->first()?->image_path ?? $project->image_path;
                                ?>
                                <img src="<?php echo e(asset('storage/' . $imagePath)); ?>"
                                     class="card-img-top" alt="<?php echo e($project->title); ?>"
                                     style="height: 200px; object-fit: cover;">
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-code fa-3x text-white"></i>
                        </div>
                    <?php endif; ?>
                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($project->title); ?></h5>
                        <p class="card-text"><?php echo e(Str::limit($project->description, 100)); ?></p>
                        <p class="text-muted small">
                            <strong>Tech:</strong> <?php echo e($project->tech_stack); ?>

                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <?php if($project->github_url): ?>
                            <a href="<?php echo e($project->github_url); ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fab fa-github"></i> View Code
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="text-center mt-5">
            <a href="<?php echo e(route('projects')); ?>" class="btn btn-primary btn-lg">View All Projects</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container text-center">
        <h2 class="display-5 fw-bold mb-4">Ready to Work Together?</h2>
        <p class="lead mb-4">Let's discuss your next project and bring your ideas to life.</p>
        <a href="<?php echo e(route('contact')); ?>" class="btn btn-light btn-lg">Get In Touch</a>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize GSAP animations
    gsap.registerPlugin(ScrollTrigger);

    // Hero section animations
    const tl = gsap.timeline();

    tl.from('.hero-title', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    })
    .from('.hero-name', {
        duration: 0.8,
        scale: 0.8,
        opacity: 0,
        ease: 'back.out(1.7)'
    }, '-=0.5')
    .from('.hero-subtitle', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
    }, '-=0.3')
    .from('.hero-description', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
    }, '-=0.2')
    .from('.hero-btn-1, .hero-btn-2, .hero-btn-3', {
        duration: 0.6,
        y: 20,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out'
    }, '-=0.2')
    .from('.hero-icon-container', {
        duration: 1.2,
        scale: 0,
        rotation: 180,
        opacity: 0,
        ease: 'elastic.out(1, 0.5)'
    }, '-=0.8');

    // Floating animation for hero icon
    gsap.to('.floating', {
        y: -20,
        duration: 2,
        ease: 'power1.inOut',
        yoyo: true,
        repeat: -1
    });

    // Background elements animation
    gsap.set('.bg-element', {
        opacity: 0.1,
        scale: 0
    });

    gsap.to('.bg-element-1', {
        scale: 1,
        rotation: 360,
        duration: 20,
        ease: 'none',
        repeat: -1
    });

    gsap.to('.bg-element-2', {
        scale: 1,
        rotation: -360,
        duration: 25,
        ease: 'none',
        repeat: -1
    });

    gsap.to('.bg-element-3', {
        scale: 1,
        rotation: 360,
        duration: 30,
        ease: 'none',
        repeat: -1
    });

    // Section titles animation
    gsap.utils.toArray('.section-title').forEach(title => {
        gsap.from(title, {
            scrollTrigger: {
                trigger: title,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            },
            y: 50,
            opacity: 0,
            duration: 1,
            ease: 'power3.out'
        });
    });

    // Skill cards animation
    gsap.utils.toArray('.skill-card').forEach((card, index) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse'
            },
            y: 60,
            opacity: 0,
            rotation: 5,
            duration: 0.8,
            delay: index * 0.1,
            ease: 'back.out(1.7)'
        });
    });

    // Project cards animation
    gsap.utils.toArray('.project-card').forEach((card, index) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse'
            },
            y: 80,
            opacity: 0,
            scale: 0.8,
            duration: 1,
            delay: index * 0.15,
            ease: 'power3.out'
        });
    });

    // Initialize all project swipers on home page
    document.querySelectorAll('.project-swiper').forEach((swiperElement, index) => {
        new Swiper(swiperElement, {
            loop: true,
            autoplay: {
                delay: 5000 + (index * 700), // Stagger autoplay
                disableOnInteraction: false,
            },
            pagination: {
                el: swiperElement.querySelector('.swiper-pagination'),
                clickable: true,
            },
            navigation: {
                nextEl: swiperElement.querySelector('.swiper-button-next'),
                prevEl: swiperElement.querySelector('.swiper-button-prev'),
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },
            speed: 1000,
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.portfolio', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel-porfolio\resources\views/home.blade.php ENDPATH**/ ?>