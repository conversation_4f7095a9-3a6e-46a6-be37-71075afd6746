<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadController extends Controller
{
    public function cv()
    {
        $filePath = 'public/umer-farooque-cv.pdf';

        if (!Storage::exists($filePath)) {
            abort(404, 'CV file not found');
        }

        return Storage::download($filePath, 'Umer-Farooque-CV.pdf');
    }
}
