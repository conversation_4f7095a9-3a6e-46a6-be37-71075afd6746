<?php $__env->startSection('title', 'Messages'); ?>
<?php $__env->startSection('page-title', 'Contact Messages'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Contact Messages</h2>
    <span class="badge bg-primary fs-6"><?php echo e($messages->total()); ?> Total Messages</span>
</div>

<?php if($messages->count() > 0): ?>
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Message</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <strong><?php echo e($message->name); ?></strong>
                            </td>
                            <td>
                                <a href="mailto:<?php echo e($message->email); ?>" class="text-decoration-none">
                                    <?php echo e($message->email); ?>

                                </a>
                            </td>
                            <td>
                                <div style="max-width: 300px;">
                                    <?php echo e(Str::limit($message->message, 80)); ?>

                                </div>
                            </td>
                            <td>
                                <div>
                                    <?php echo e($message->created_at->format('M d, Y')); ?>

                                    <br>
                                    <small class="text-muted"><?php echo e($message->created_at->format('g:i A')); ?></small>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.messages.show', $message)); ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="mailto:<?php echo e($message->email); ?>" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-reply"></i> Reply
                                    </a>
                                    <form action="<?php echo e(route('admin.messages.destroy', $message)); ?>" method="POST" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                onclick="return confirm('Are you sure you want to delete this message?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($messages->hasPages()): ?>
        <div class="d-flex justify-content-center mt-4">
            <?php echo e($messages->links()); ?>

        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="card border-0 shadow-sm">
        <div class="card-body text-center py-5">
            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
            <h5>No Messages Yet</h5>
            <p class="text-muted">Messages from the contact form will appear here.</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-primary" target="_blank">
                <i class="fas fa-external-link-alt me-2"></i>View Contact Form
            </a>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel-porfolio\resources\views/admin/messages/index.blade.php ENDPATH**/ ?>