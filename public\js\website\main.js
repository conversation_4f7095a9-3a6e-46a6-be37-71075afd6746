// Website Main JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Website JS loaded');
    
    // Initialize Page Loader
    initPageLoader();
    
    // Initialize AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    }
    
    // Initialize GSAP animations only if elements exist
    initGSAPAnimations();
    
    // Initialize Swiper if on projects page
    initSwiper();
});

// Page Loader Function
function initPageLoader() {
    const loader = document.getElementById('pageLoader');
    const progressBar = document.querySelector('.loader-progress-bar');
    const percentage = document.querySelector('.loader-percentage');
    
    if (!loader) return;
    
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;
        
        if (progressBar) {
            progressBar.style.width = progress + '%';
        }
        if (percentage) {
            percentage.textContent = Math.round(progress) + '%';
        }
        
        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 500);
            }, 500);
        }
    }, 100);
}

// GSAP Animations
function initGSAPAnimations() {
    if (typeof gsap === 'undefined') return;
    
    // Register ScrollTrigger plugin
    if (typeof ScrollTrigger !== 'undefined') {
        gsap.registerPlugin(ScrollTrigger);
    }
    
    // Hero Section Animation (only if exists)
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        const heroTitle = heroSection.querySelector('.hero-title');
        const heroSubtitle = heroSection.querySelector('.hero-subtitle');
        const heroDescription = heroSection.querySelector('.hero-description');
        const heroButtons = heroSection.querySelector('.hero-buttons');
        
        // Set initial states
        gsap.set([heroTitle, heroSubtitle, heroDescription, heroButtons], {
            opacity: 0,
            y: 50
        });
        
        // Animate elements
        const tl = gsap.timeline({ delay: 1 });
        
        if (heroTitle) {
            tl.to(heroTitle, {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: "power3.out"
            });
        }
        
        if (heroSubtitle) {
            tl.to(heroSubtitle, {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: "power3.out"
            }, "-=0.5");
        }
        
        if (heroDescription) {
            tl.to(heroDescription, {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: "power3.out"
            }, "-=0.5");
        }
        
        if (heroButtons) {
            tl.to(heroButtons, {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: "power3.out"
            }, "-=0.5");
        }
    }
    
    // Projects Section Animation (only if exists)
    const projectsSection = document.querySelector('.projects-section');
    if (projectsSection && typeof ScrollTrigger !== 'undefined') {
        const projectCards = projectsSection.querySelectorAll('.project-card');
        
        if (projectCards.length > 0) {
            gsap.fromTo(projectCards, 
                {
                    opacity: 0,
                    y: 50,
                    scale: 0.9
                },
                {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    duration: 0.8,
                    stagger: 0.2,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: projectsSection,
                        start: "top 80%",
                        end: "bottom 20%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }
    }
    
    // About Section Animation (only if exists)
    const aboutSection = document.querySelector('.about-section');
    if (aboutSection && typeof ScrollTrigger !== 'undefined') {
        const aboutContent = aboutSection.querySelectorAll('.about-content > *');
        
        if (aboutContent.length > 0) {
            gsap.fromTo(aboutContent,
                {
                    opacity: 0,
                    x: -50
                },
                {
                    opacity: 1,
                    x: 0,
                    duration: 1,
                    stagger: 0.2,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: aboutSection,
                        start: "top 80%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }
    }
    
    // Skills Animation (only if exists)
    const skillsSection = document.querySelector('.skills-section');
    if (skillsSection && typeof ScrollTrigger !== 'undefined') {
        const skillItems = skillsSection.querySelectorAll('.skill-item');
        
        if (skillItems.length > 0) {
            gsap.fromTo(skillItems,
                {
                    opacity: 0,
                    scale: 0.8
                },
                {
                    opacity: 1,
                    scale: 1,
                    duration: 0.6,
                    stagger: 0.1,
                    ease: "back.out(1.7)",
                    scrollTrigger: {
                        trigger: skillsSection,
                        start: "top 80%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }
    }
}

// Swiper Initialization
function initSwiper() {
    if (typeof Swiper === 'undefined') return;
    
    // Project Images Swiper
    const projectSwipers = document.querySelectorAll('.project-swiper');
    projectSwipers.forEach(swiperEl => {
        new Swiper(swiperEl, {
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            pagination: {
                el: swiperEl.querySelector('.swiper-pagination'),
                clickable: true,
            },
            navigation: {
                nextEl: swiperEl.querySelector('.swiper-button-next'),
                prevEl: swiperEl.querySelector('.swiper-button-prev'),
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            }
        });
    });
}

// Smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Contact form enhancement
const contactForm = document.querySelector('#contactForm');
if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
        }
    });
}
