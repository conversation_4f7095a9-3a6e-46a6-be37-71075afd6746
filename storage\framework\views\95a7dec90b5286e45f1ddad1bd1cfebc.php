<!-- Dashboard Header -->
<header class="dashboard-header">
    <div class="dashboard-header-content">
        <!-- Left Side -->
        <div class="dashboard-header-left">
            <!-- Mobile Menu Toggle -->
            <button class="dashboard-mobile-menu-toggle" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- Breadcrumb -->
            <nav class="dashboard-breadcrumb">
                <?php if(isset($breadcrumbs)): ?>
                    <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="dashboard-breadcrumb-item">
                            <?php if($index > 0): ?>
                                <span class="dashboard-breadcrumb-separator">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            <?php endif; ?>
                            <?php if(isset($breadcrumb['url']) && !$loop->last): ?>
                                <a href="<?php echo e($breadcrumb['url']); ?>" style="color: var(--dashboard-primary); text-decoration: none;">
                                    <?php echo e($breadcrumb['title']); ?>

                                </a>
                            <?php else: ?>
                                <span style="color: var(--dashboard-gray-900); font-weight: 500;">
                                    <?php echo e($breadcrumb['title']); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="dashboard-breadcrumb-item">
                        <span style="color: var(--dashboard-gray-900); font-weight: 500;">
                            <?php echo $__env->yieldContent('page-title', 'Dashboard'); ?>
                        </span>
                    </div>
                <?php endif; ?>
            </nav>
        </div>
        
        <!-- Right Side -->
        <div class="dashboard-header-right">
            <!-- Search -->
            <div class="dashboard-search">
                <div class="dashboard-search-icon">
                    <i class="fas fa-search"></i>
                </div>
                <input type="text" 
                       class="dashboard-search-input" 
                       placeholder="Search projects, messages..."
                       id="globalSearch">
            </div>
            
            <!-- Notifications -->
            <div class="dropdown">
                <button class="btn btn-link p-2 text-decoration-none" 
                        type="button" 
                        id="notificationsDropdown" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false"
                        style="color: var(--dashboard-gray-600); position: relative;">
                    <i class="fas fa-bell" style="font-size: 1.1rem;"></i>
                    <?php
                        // Temporarily disabled for testing
                        $unreadMessages = 0; // App\Models\Message::whereNull('read_at')->count();
                    ?>
                    <?php if($unreadMessages > 0): ?>
                        <span style="position: absolute; top: 0; right: 0; background: var(--dashboard-danger); color: white; border-radius: 50%; width: 18px; height: 18px; font-size: 0.625rem; display: flex; align-items: center; justify-content: center; font-weight: 600;">
                            <?php echo e($unreadMessages > 9 ? '9+' : $unreadMessages); ?>

                        </span>
                    <?php endif; ?>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="width: 320px;">
                    <li class="dropdown-header d-flex justify-content-between align-items-center">
                        <span style="font-weight: 600;">Notifications</span>
                        <?php if($unreadMessages > 0): ?>
                            <span class="dashboard-badge dashboard-badge-danger"><?php echo e($unreadMessages); ?> new</span>
                        <?php endif; ?>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    
                    <?php if($unreadMessages > 0): ?>
                        <?php
                            // Temporarily disabled for testing
                            $recentMessages = collect([]); // App\Models\Message::whereNull('read_at')->latest()->take(3)->get();
                        ?>
                        <?php $__currentLoopData = $recentMessages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a class="dropdown-item py-3" href="<?php echo e(route('admin.messages.show', $message)); ?>">
                                    <div class="d-flex align-items-start gap-3">
                                        <div style="width: 32px; height: 32px; background: var(--dashboard-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600; flex-shrink: 0;">
                                            <?php echo e(substr($message->name, 0, 1)); ?>

                                        </div>
                                        <div style="flex: 1; min-width: 0;">
                                            <div style="font-weight: 500; font-size: 0.875rem; color: var(--dashboard-gray-900); margin-bottom: 0.25rem;">
                                                New message from <?php echo e($message->name); ?>

                                            </div>
                                            <div style="font-size: 0.75rem; color: var(--dashboard-gray-600); line-height: 1.3;">
                                                <?php echo e(Str::limit($message->message, 60)); ?>

                                            </div>
                                            <div style="font-size: 0.625rem; color: var(--dashboard-gray-500); margin-top: 0.25rem;">
                                                <?php echo e($message->created_at->diffForHumans()); ?>

                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-center py-2" href="<?php echo e(route('admin.messages.index')); ?>">
                                <small style="color: var(--dashboard-primary); font-weight: 500;">View all messages</small>
                            </a>
                        </li>
                    <?php else: ?>
                        <li>
                            <div class="dropdown-item-text text-center py-4">
                                <i class="fas fa-bell-slash text-muted mb-2" style="font-size: 2rem;"></i>
                                <div style="color: var(--dashboard-gray-500); font-size: 0.875rem;">No new notifications</div>
                            </div>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <!-- Quick Actions -->
            <div class="dropdown">
                <button class="btn btn-primary btn-sm dropdown-toggle" 
                        type="button" 
                        id="quickActionsDropdown" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <i class="fas fa-plus me-1"></i>
                    Quick Add
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="quickActionsDropdown">
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('admin.projects.create')); ?>">
                            <i class="fas fa-code me-2"></i>New Project
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('home')); ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Website
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- User Menu -->
            <div class="dropdown">
                <button class="dashboard-user-trigger" 
                        type="button" 
                        id="userMenuDropdown" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <div class="dashboard-user-avatar">
                        <?php echo e(substr(auth()->user()->name ?? 'U', 0, 1)); ?>

                    </div>
                    <div class="dashboard-user-info d-none d-md-block">
                        <div class="dashboard-user-name"><?php echo e(auth()->user()->name ?? 'Admin User'); ?></div>
                        <div class="dashboard-user-role">Administrator</div>
                    </div>
                    <i class="fas fa-chevron-down" style="color: var(--dashboard-gray-400); font-size: 0.75rem;"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
                    <li class="dropdown-header">
                        <div style="font-weight: 600;"><?php echo e(auth()->user()->name ?? 'Admin User'); ?></div>
                        <div style="font-size: 0.75rem; color: var(--dashboard-gray-500);"><?php echo e(auth()->user()->email ?? '<EMAIL>'); ?></div>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('profile.edit')); ?>">
                            <i class="fas fa-user-cog me-2"></i>Profile Settings
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('home')); ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Website
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline w-100">
                            <?php echo csrf_field(); ?>
                            <button type="submit" 
                                    class="dropdown-item text-danger"
                                    onclick="return confirm('Are you sure you want to logout?')">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</header>

<script>
// Global search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('globalSearch');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // You can implement global search logic here
                    // For now, redirect to projects with search
                    window.location.href = `<?php echo e(route('admin.projects.index')); ?>?search=${encodeURIComponent(query)}`;
                }
            }
        });
    }
});
</script>
<?php /**PATH D:\laravel-porfolio\resources\views/dashboard/partials/header.blade.php ENDPATH**/ ?>