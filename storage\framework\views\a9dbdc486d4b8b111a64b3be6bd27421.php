<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', 'Umer Farooque - Full Stack Developer'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <!-- AOS CSS for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
            pointer-events: none;
        }

        .section-padding {
            padding: 80px 0;
        }

        .project-card {
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            border-radius: 20px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .project-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        /* Hero background elements */
        .hero-bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .bg-element {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            backdrop-filter: blur(10px);
        }

        .bg-element-1 {
            width: 200px;
            height: 200px;
            top: 10%;
            right: 10%;
        }

        .bg-element-2 {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 15%;
        }

        .bg-element-3 {
            width: 100px;
            height: 100px;
            top: 60%;
            right: 30%;
        }

        /* Skill card hover effects */
        .skill-card {
            transition: all 0.3s ease;
        }

        .skill-card:hover {
            transform: translateY(-5px) rotate(2deg);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        /* Enhanced floating animation */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .project-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
        }

        .project-swiper {
            border-radius: 15px;
        }

        .swiper-pagination-bullet {
            background: #667eea;
            opacity: 0.7;
        }

        .swiper-pagination-bullet-active {
            background: #764ba2;
            opacity: 1;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: #667eea;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-top: -20px;
        }

        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 16px;
            font-weight: bold;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .section-title {
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }

        /* Animation classes */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
        }

        .fade-in-left {
            opacity: 0;
            transform: translateX(-30px);
        }

        .fade-in-right {
            opacity: 0;
            transform: translateX(30px);
        }

        .scale-in {
            opacity: 0;
            transform: scale(0.8);
        }

        /* Floating animation */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('home')); ?>">Umer Farooque</a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('home')); ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('about')); ?>">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('projects')); ?>">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('contact')); ?>">Contact</a>
                    </li>
                    <?php if(auth()->guard()->check()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('admin.dashboard')); ?>">Admin</a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="nav-link btn btn-link">Logout</button>
                            </form>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('login')); ?>">Login</a>
                        </li>
                    <?php endif; ?>
                </ul>
                <div class="d-flex ms-3">
                    <a href="https://github.com/umerfarooque" class="btn btn-outline-primary btn-sm me-2" target="_blank" title="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/in/umerfarooque" class="btn btn-outline-primary btn-sm" target="_blank" title="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="margin-top: 76px;">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Umer Farooque</h5>
                    <p>Full Stack Developer specializing in Laravel, WordPress, PHP, JavaScript, and modern web technologies.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Connect With Me</h5>
                    <div class="social-links">
                        <a href="https://github.com/umerfarooque" class="text-white me-3" target="_blank">
                            <i class="fab fa-github fa-2x"></i>
                        </a>
                        <a href="https://linkedin.com/in/umerfarooque" class="text-white me-3" target="_blank">
                            <i class="fab fa-linkedin fa-2x"></i>
                        </a>
                        <a href="mailto:<EMAIL>" class="text-white">
                            <i class="fas fa-envelope fa-2x"></i>
                        </a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; <?php echo e(date('Y')); ?> Umer Farooque. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Register GSAP plugins
        gsap.registerPlugin(ScrollTrigger);

        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // GSAP Animations
        document.addEventListener('DOMContentLoaded', function() {
            // Hero section animation
            gsap.timeline()
                .from('.hero-section h1', {duration: 1, y: 50, opacity: 0, ease: "power3.out"})
                .from('.hero-section p', {duration: 1, y: 30, opacity: 0, ease: "power3.out"}, "-=0.5")
                .from('.hero-section .btn', {duration: 1, y: 30, opacity: 0, ease: "power3.out"}, "-=0.3");

            // Navbar animation on scroll
            ScrollTrigger.create({
                start: "top -80",
                end: 99999,
                toggleClass: {className: "scrolled", targets: ".navbar"}
            });

            // Project cards animation
            gsap.utils.toArray('.project-card').forEach((card, i) => {
                gsap.from(card, {
                    duration: 1,
                    y: 60,
                    opacity: 0,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: card,
                        start: "top 85%",
                        end: "bottom 15%",
                        toggleActions: "play none none reverse"
                    },
                    delay: i * 0.1
                });
            });

            // Section titles animation
            gsap.utils.toArray('.section-title').forEach(title => {
                gsap.from(title, {
                    duration: 1,
                    x: -50,
                    opacity: 0,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: title,
                        start: "top 85%",
                        end: "bottom 15%",
                        toggleActions: "play none none reverse"
                    }
                });
            });

            // Parallax effect for hero background
            gsap.to('.hero-section::before', {
                yPercent: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: '.hero-section',
                    start: "top bottom",
                    end: "bottom top",
                    scrub: true
                }
            });
        });

        // Initialize Swiper for project images
        function initProjectSwiper(container) {
            return new Swiper(container, {
                loop: true,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: container + ' .swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: container + ' .swiper-button-next',
                    prevEl: container + ' .swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                speed: 800,
            });
        }

        // Initialize all project swipers
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.project-swiper').forEach((swiper, index) => {
                initProjectSwiper('.project-swiper-' + index);
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\laravel-porfolio\resources\views/layouts/portfolio.blade.php ENDPATH**/ ?>