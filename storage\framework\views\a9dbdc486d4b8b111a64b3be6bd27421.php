<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', 'Umer Farooque - Full Stack Developer'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <!-- AOS CSS for animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
            pointer-events: none;
        }

        .section-padding {
            padding: 80px 0;
        }

        .project-card {
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            border-radius: 20px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .project-card:hover {
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .project-card-wrapper {
            perspective: 1000px;
        }

        .projects-section {
            overflow: hidden;
        }

        .projects-section .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Hero background elements */
        .hero-bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .bg-element {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            backdrop-filter: blur(10px);
        }

        .bg-element-1 {
            width: 200px;
            height: 200px;
            top: 10%;
            right: 10%;
        }

        .bg-element-2 {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 15%;
        }

        .bg-element-3 {
            width: 100px;
            height: 100px;
            top: 60%;
            right: 30%;
        }

        /* Skill card hover effects */
        .skill-card {
            transition: all 0.3s ease;
        }

        .skill-card:hover {
            transform: translateY(-5px) rotate(2deg);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        /* Hero animation initial states */
        .hero-title,
        .hero-subtitle,
        .hero-description,
        .hero-buttons,
        .hero-icon-container {
            opacity: 0;
        }

        .hero-title {
            transform: translateY(50px);
        }

        .hero-subtitle,
        .hero-description {
            transform: translateY(30px);
        }

        .hero-buttons {
            transform: translateY(20px);
        }

        .hero-icon-container {
            transform: scale(0) rotate(180deg);
        }

        .hero-name {
            opacity: 0;
            transform: scale(0.8);
        }

        /* Fallback CSS animations if GSAP fails */
        .hero-section.css-fallback .hero-title {
            animation: fadeInUp 1.2s ease-out 0.5s forwards;
        }

        .hero-section.css-fallback .hero-subtitle {
            animation: fadeInUp 0.8s ease-out 1.2s forwards;
        }

        .hero-section.css-fallback .hero-description {
            animation: fadeInUp 0.8s ease-out 1.5s forwards;
        }

        .hero-section.css-fallback .hero-buttons {
            animation: fadeInUp 0.8s ease-out 1.8s forwards;
        }

        .hero-section.css-fallback .hero-icon-container {
            animation: scaleIn 1.5s ease-out 2s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        /* Enhanced floating animation - disabled initially for GSAP control */
        .floating.gsap-animated {
            animation: none;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .project-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
        }

        .project-swiper {
            border-radius: 15px;
        }

        .swiper-pagination-bullet {
            background: #667eea;
            opacity: 0.7;
        }

        .swiper-pagination-bullet-active {
            background: #764ba2;
            opacity: 1;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: #667eea;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-top: -20px;
        }

        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 16px;
            font-weight: bold;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .section-title {
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        footer {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff03" points="0,0 1000,1000 0,1000"/></svg>');
            pointer-events: none;
            animation: footerBgMove 20s ease-in-out infinite;
        }

        @keyframes footerBgMove {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-10px) translateY(-5px); }
            50% { transform: translateX(10px) translateY(5px); }
            75% { transform: translateX(-5px) translateY(10px); }
        }

        .footer-content {
            position: relative;
            z-index: 1;
        }

        .footer-brand {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .footer-description {
            color: #b8c5d6;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .footer-section h5 {
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .footer-section h5::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.8rem;
        }

        .footer-links a {
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .footer-links a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .footer-links a i {
            margin-right: 0.5rem;
            width: 16px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .social-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 2rem;
            margin-top: 3rem;
            text-align: center;
        }

        .footer-bottom p {
            color: #b8c5d6;
            margin: 0;
        }

        .footer-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .footer-skill-tag {
            background: rgba(255, 255, 255, 0.1);
            color: #b8c5d6;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .footer-skill-tag:hover {
            background: rgba(102, 126, 234, 0.2);
            color: white;
        }

        /* Footer responsive styles */
        @media (max-width: 768px) {
            .footer-brand {
                font-size: 1.5rem;
                text-align: center;
            }

            .footer-description {
                text-align: center;
            }

            .social-links {
                justify-content: center;
            }

            .footer-section h5 {
                text-align: center;
            }

            .footer-section h5::after {
                left: 50%;
                transform: translateX(-50%);
            }

            .footer-links {
                text-align: center;
            }

            .footer-skills {
                justify-content: center;
            }

            .footer-bottom {
                text-align: center;
            }

            .footer-bottom .col-md-6 {
                text-align: center !important;
                margin-bottom: 1rem;
            }
        }

        /* Animation classes */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
        }

        .fade-in-left {
            opacity: 0;
            transform: translateX(-30px);
        }

        .fade-in-right {
            opacity: 0;
            transform: translateX(30px);
        }

        .scale-in {
            opacity: 0;
            transform: scale(0.8);
        }

        /* Floating animation */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('home')); ?>">Umer Farooque</a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('home')); ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('about')); ?>">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('projects')); ?>">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo e(route('contact')); ?>">Contact</a>
                    </li>
                    <?php if(auth()->guard()->check()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('admin.dashboard')); ?>">Admin</a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="nav-link btn btn-link">Logout</button>
                            </form>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('login')); ?>">Login</a>
                        </li>
                    <?php endif; ?>
                </ul>
                <div class="d-flex ms-3">
                    <a href="https://github.com/umerfarooque" class="btn btn-outline-primary btn-sm me-2" target="_blank" title="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/in/umerfarooque" class="btn btn-outline-primary btn-sm" target="_blank" title="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="margin-top: 76px;">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="py-5">
        <div class="container footer-content">
            <div class="row">
                <!-- Brand & Description -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-brand">Umer Farooque</div>
                    <p class="footer-description">
                        Passionate Full Stack Developer crafting exceptional digital experiences with modern technologies.
                        Specializing in Laravel, WordPress, and JavaScript ecosystems.
                    </p>
                    <div class="social-links">
                        <a href="https://github.com/umerfarooque" class="social-link" target="_blank" title="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://linkedin.com/in/umerfarooque" class="social-link" target="_blank" title="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="mailto:<EMAIL>" class="social-link" title="Email">
                            <i class="fas fa-envelope"></i>
                        </a>
                        <a href="https://twitter.com/umerfarooque" class="social-link" target="_blank" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4 footer-section">
                    <h5>Quick Links</h5>
                    <ul class="footer-links">
                        <li><a href="<?php echo e(route('home')); ?>"><i class="fas fa-home"></i>Home</a></li>
                        <li><a href="<?php echo e(route('about')); ?>"><i class="fas fa-user"></i>About</a></li>
                        <li><a href="<?php echo e(route('projects')); ?>"><i class="fas fa-code"></i>Projects</a></li>
                        <li><a href="<?php echo e(route('contact')); ?>"><i class="fas fa-envelope"></i>Contact</a></li>
                        <li><a href="<?php echo e(route('download.cv')); ?>"><i class="fas fa-download"></i>Download CV</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div class="col-lg-3 col-md-6 mb-4 footer-section">
                    <h5>Services</h5>
                    <ul class="footer-links">
                        <li><a href="#"><i class="fas fa-laptop-code"></i>Web Development</a></li>
                        <li><a href="#"><i class="fab fa-laravel"></i>Laravel Applications</a></li>
                        <li><a href="#"><i class="fab fa-wordpress"></i>WordPress Development</a></li>
                        <li><a href="#"><i class="fas fa-mobile-alt"></i>Responsive Design</a></li>
                        <li><a href="#"><i class="fas fa-database"></i>Database Design</a></li>
                    </ul>
                </div>

                <!-- Technologies -->
                <div class="col-lg-3 col-md-6 mb-4 footer-section">
                    <h5>Technologies</h5>
                    <div class="footer-skills">
                        <span class="footer-skill-tag">Laravel</span>
                        <span class="footer-skill-tag">PHP</span>
                        <span class="footer-skill-tag">JavaScript</span>
                        <span class="footer-skill-tag">jQuery</span>
                        <span class="footer-skill-tag">Bootstrap</span>
                        <span class="footer-skill-tag">MySQL</span>
                        <span class="footer-skill-tag">WordPress</span>
                        <span class="footer-skill-tag">REST API</span>
                        <span class="footer-skill-tag">Git</span>
                        <span class="footer-skill-tag">GSAP</span>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; <?php echo e(date('Y')); ?> Umer Farooque. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>Built with <i class="fas fa-heart text-danger"></i> using Laravel & GSAP</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Register GSAP plugins
        gsap.registerPlugin(ScrollTrigger);

        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // GSAP Animations
        document.addEventListener('DOMContentLoaded', function() {
            // Hero section animation
            gsap.timeline()
                .from('.hero-section h1', {duration: 1, y: 50, opacity: 0, ease: "power3.out"})
                .from('.hero-section p', {duration: 1, y: 30, opacity: 0, ease: "power3.out"}, "-=0.5")
                .from('.hero-section .btn', {duration: 1, y: 30, opacity: 0, ease: "power3.out"}, "-=0.3");

            // Navbar animation on scroll
            ScrollTrigger.create({
                start: "top -80",
                end: 99999,
                toggleClass: {className: "scrolled", targets: ".navbar"}
            });



            // Section titles animation
            gsap.utils.toArray('.section-title').forEach(title => {
                gsap.from(title, {
                    duration: 1,
                    x: -50,
                    opacity: 0,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: title,
                        start: "top 85%",
                        end: "bottom 15%",
                        toggleActions: "play none none reverse"
                    }
                });
            });

            // Parallax effect for hero background
            gsap.to('.hero-section::before', {
                yPercent: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: '.hero-section',
                    start: "top bottom",
                    end: "bottom top",
                    scrub: true
                }
            });

            // Footer animations
            gsap.utils.toArray('.footer-section').forEach((section, index) => {
                gsap.from(section, {
                    scrollTrigger: {
                        trigger: section,
                        start: 'top 90%',
                        end: 'bottom 10%',
                        toggleActions: 'play none none reverse'
                    },
                    y: 50,
                    opacity: 0,
                    duration: 0.8,
                    delay: index * 0.1,
                    ease: 'power2.out'
                });
            });

            // Footer brand animation
            gsap.from('.footer-brand', {
                scrollTrigger: {
                    trigger: '.footer-brand',
                    start: 'top 90%',
                    end: 'bottom 10%',
                    toggleActions: 'play none none reverse'
                },
                scale: 0.8,
                opacity: 0,
                duration: 1,
                ease: 'back.out(1.7)'
            });

            // Social links stagger animation
            gsap.from('.social-link', {
                scrollTrigger: {
                    trigger: '.social-links',
                    start: 'top 90%',
                    end: 'bottom 10%',
                    toggleActions: 'play none none reverse'
                },
                scale: 0,
                opacity: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: 'back.out(1.7)'
            });

            // Footer skills animation
            gsap.from('.footer-skill-tag', {
                scrollTrigger: {
                    trigger: '.footer-skills',
                    start: 'top 90%',
                    end: 'bottom 10%',
                    toggleActions: 'play none none reverse'
                },
                y: 20,
                opacity: 0,
                duration: 0.5,
                stagger: 0.05,
                ease: 'power2.out'
            });
        });

        // Initialize Swiper for project images
        function initProjectSwiper(container) {
            return new Swiper(container, {
                loop: true,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: container + ' .swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: container + ' .swiper-button-next',
                    prevEl: container + ' .swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                speed: 800,
            });
        }

        // Initialize all project swipers
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.project-swiper').forEach((swiper, index) => {
                initProjectSwiper('.project-swiper-' + index);
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\laravel-porfolio\resources\views/layouts/portfolio.blade.php ENDPATH**/ ?>