/* Website Main CSS */

/* Page Loader Styles */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

.page-loader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    animation: logoFloat 3s ease-in-out infinite;
}

.loader-logo svg {
    width: 100%;
    height: 100%;
}

.loader-text {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: 3px;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.loader-subtitle {
    font-size: 0.9rem;
    color: #b8c5d6;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.loader-percentage {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    animation: percentageGlow 2s ease-in-out infinite alternate;
}

.loader-progress {
    width: 200px;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}

.loader-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes percentageGlow {
    0% { 
        color: #667eea;
        text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    }
    100% { 
        color: #764ba2;
        text-shadow: 0 0 30px rgba(118, 75, 162, 0.8), 0 0 40px rgba(102, 126, 234, 0.5);
    }
}

/* Global Styles */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Text Gradient */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Styles */
.navbar {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(26, 26, 46, 0.98);
    padding: 0.5rem 0;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon svg {
    width: 40px;
    height: 40px;
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.logo-name {
    font-size: 1rem;
    font-weight: 700;
    color: white;
    letter-spacing: 1px;
}

.logo-title {
    font-size: 0.7rem;
    color: #b8c5d6;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: #667eea !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #b8c5d6;
    margin-bottom: 1.5rem;
    font-weight: 300;
}

.hero-description {
    font-size: 1.1rem;
    color: #d1d5db;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.hero-buttons .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.hero-buttons .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.hero-buttons .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.hero-buttons .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Hero Image */
.hero-image {
    position: relative;
    z-index: 2;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: cardFloat 6s ease-in-out infinite;
}

.code-snippet {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
    font-family: 'Fira Code', monospace;
}

.code-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    display: flex;
    gap: 8px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot.red { background: #ff5f56; }
.dot.yellow { background: #ffbd2e; }
.dot.green { background: #27ca3f; }

.code-body {
    padding: 20px;
    font-size: 0.9rem;
    line-height: 1.6;
}

.code-line {
    margin-bottom: 8px;
}

.code-line.indent {
    padding-left: 20px;
}

.keyword { color: #ff79c6; }
.variable { color: #8be9fd; }
.property { color: #50fa7b; }
.string { color: #f1fa8c; }

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 3rem;
}

/* Project Cards */
.project-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-content {
    padding: 1.5rem;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
}

.project-description {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Skill Cards */
.skill-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.skill-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.skill-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.skill-card h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.skill-card p {
    color: #6c757d;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .logo-text {
        display: none;
    }
}
