<?php $__env->startSection('title', 'Projects - Umer Farooque'); ?>

<?php $__env->startSection('content'); ?>
<!-- Projects Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-4">My Projects</h1>
                <p class="lead">
                    A showcase of my work in web development, from Laravel applications to WordPress sites.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="section-padding">
    <div class="container">
        <?php if($projects->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                    <div class="card project-card h-100 border-0 shadow-sm">
                        <?php if($project->images->count() > 0 || $project->image_path): ?>
                            <div class="project-image-container">
                                <?php if($project->images->count() > 1): ?>
                                    <!-- Swiper for multiple images -->
                                    <div class="swiper project-swiper project-swiper-<?php echo e($loop->index); ?>">
                                        <div class="swiper-wrapper">
                                            <?php $__currentLoopData = $project->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="swiper-slide">
                                                    <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                                         class="card-img-top" alt="<?php echo e($project->title); ?>"
                                                         style="height: 250px; object-fit: cover;">
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="swiper-pagination"></div>
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                <?php else: ?>
                                    <!-- Single image -->
                                    <?php
                                        $imagePath = $project->images->first()?->image_path ?? $project->image_path;
                                    ?>
                                    <img src="<?php echo e(asset('storage/' . $imagePath)); ?>"
                                         class="card-img-top" alt="<?php echo e($project->title); ?>"
                                         style="height: 250px; object-fit: cover;">
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="bg-primary d-flex align-items-center justify-content-center" style="height: 250px;">
                                <i class="fas fa-code fa-4x text-white"></i>
                            </div>
                        <?php endif; ?>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo e($project->title); ?></h5>
                            <p class="card-text flex-grow-1"><?php echo e($project->description); ?></p>
                            <div class="mt-auto">
                                <p class="text-muted small mb-3">
                                    <strong>Technologies:</strong> <?php echo e($project->tech_stack); ?>

                                </p>
                                <?php if($project->github_url): ?>
                                    <a href="<?php echo e($project->github_url); ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fab fa-github"></i> View Code
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body py-5">
                            <i class="fas fa-code fa-4x text-muted mb-4"></i>
                            <h3>No Projects Yet</h3>
                            <p class="text-muted">Projects will be displayed here once they are added through the admin panel.</p>
                            <?php if(auth()->guard()->check()): ?>
                                <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn btn-primary">Add First Project</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-light">
    <div class="container text-center">
        <h2 class="display-5 fw-bold mb-4">Interested in Working Together?</h2>
        <p class="lead mb-4">I'm always open to discussing new opportunities and exciting projects.</p>
        <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">Let's Talk</a>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize GSAP animations
    gsap.registerPlugin(ScrollTrigger);

    // Page header animation
    gsap.from('.page-header h1', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    });

    gsap.from('.page-header p', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        delay: 0.3,
        ease: 'power2.out'
    });

    // Project cards stagger animation
    gsap.utils.toArray('.project-card').forEach((card, index) => {
        gsap.from(card, {
            scrollTrigger: {
                trigger: card,
                start: 'top 85%',
                end: 'bottom 15%',
                toggleActions: 'play none none reverse'
            },
            y: 80,
            opacity: 0,
            scale: 0.9,
            rotation: 2,
            duration: 1,
            delay: index * 0.1,
            ease: 'power3.out'
        });

        // Hover animation
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                y: -10,
                scale: 1.02,
                duration: 0.3,
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                y: 0,
                scale: 1,
                duration: 0.3,
                ease: 'power2.out'
            });
        });
    });

    // CTA section animation
    gsap.from('.bg-light h2', {
        scrollTrigger: {
            trigger: '.bg-light',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        },
        y: 50,
        opacity: 0,
        duration: 1,
        ease: 'power3.out'
    });

    gsap.from('.bg-light p', {
        scrollTrigger: {
            trigger: '.bg-light',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        },
        y: 30,
        opacity: 0,
        duration: 0.8,
        delay: 0.2,
        ease: 'power2.out'
    });

    gsap.from('.bg-light .btn', {
        scrollTrigger: {
            trigger: '.bg-light',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        },
        y: 20,
        opacity: 0,
        duration: 0.6,
        delay: 0.4,
        ease: 'power2.out'
    });

    // Initialize all project swipers
    document.querySelectorAll('.project-swiper').forEach((swiperElement, index) => {
        new Swiper(swiperElement, {
            loop: true,
            autoplay: {
                delay: 4000 + (index * 500), // Stagger autoplay
                disableOnInteraction: false,
            },
            pagination: {
                el: swiperElement.querySelector('.swiper-pagination'),
                clickable: true,
            },
            navigation: {
                nextEl: swiperElement.querySelector('.swiper-button-next'),
                prevEl: swiperElement.querySelector('.swiper-button-prev'),
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },
            speed: 800,
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.portfolio', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel-porfolio\resources\views/projects.blade.php ENDPATH**/ ?>