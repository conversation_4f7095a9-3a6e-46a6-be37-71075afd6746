<?php $__env->startSection('title', 'Manage Projects'); ?>
<?php $__env->startSection('page-title', 'Projects'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Manage Projects</h2>
    <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add New Project
    </a>
</div>

<?php if($projects->count() > 0): ?>
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Title</th>
                            <th>Tech Stack</th>
                            <th>GitHub URL</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?php echo e($project->title); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo e(Str::limit($project->description, 60)); ?></small>
                                </div>
                            </td>
                            <td><?php echo e($project->tech_stack); ?></td>
                            <td>
                                <?php if($project->github_url): ?>
                                    <a href="<?php echo e($project->github_url); ?>" target="_blank" class="text-decoration-none">
                                        <i class="fab fa-github"></i> View
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($project->created_at->format('M d, Y')); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.projects.show', $project)); ?>" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.projects.edit', $project)); ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.projects.destroy', $project)); ?>" method="POST" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                onclick="return confirm('Are you sure you want to delete this project?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($projects->hasPages()): ?>
        <div class="d-flex justify-content-center mt-4">
            <?php echo e($projects->links()); ?>

        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="card border-0 shadow-sm">
        <div class="card-body text-center py-5">
            <i class="fas fa-code fa-3x text-muted mb-3"></i>
            <h5>No Projects Yet</h5>
            <p class="text-muted mb-4">Start by adding your first project to showcase your work.</p>
            <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Project
            </a>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel-porfolio\resources\views/admin/projects/index.blade.php ENDPATH**/ ?>