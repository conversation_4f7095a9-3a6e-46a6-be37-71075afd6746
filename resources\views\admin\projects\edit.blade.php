@extends('layouts.admin')

@section('title', 'Edit Project')
@section('page-title', 'Edit Project')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Edit Project: {{ $project->title }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.projects.update', $project) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Project Title *</label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title', $project->title) }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="5" required>{{ old('description', $project->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="tech_stack" class="form-label">Tech Stack *</label>
                        <input type="text" class="form-control @error('tech_stack') is-invalid @enderror" 
                               id="tech_stack" name="tech_stack" value="{{ old('tech_stack', $project->tech_stack) }}" 
                               placeholder="e.g., Laravel, PHP, JavaScript, Bootstrap" required>
                        @error('tech_stack')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="github_url" class="form-label">GitHub URL</label>
                        <input type="url" class="form-control @error('github_url') is-invalid @enderror" 
                               id="github_url" name="github_url" value="{{ old('github_url', $project->github_url) }}" 
                               placeholder="https://github.com/username/repository">
                        @error('github_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="screenshot_url" class="form-label">Screenshot URL</label>
                        <input type="url" class="form-control @error('screenshot_url') is-invalid @enderror" 
                               id="screenshot_url" name="screenshot_url" value="{{ old('screenshot_url', $project->screenshot_url) }}" 
                               placeholder="https://example.com/screenshot.jpg">
                        @error('screenshot_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Project
                        </button>
                        <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a href="{{ route('admin.projects.show', $project) }}" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>View
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h6 class="card-title mb-0">Project Info</h6>
            </div>
            <div class="card-body">
                <p><strong>Created:</strong> {{ $project->created_at->format('M d, Y') }}</p>
                <p><strong>Last Updated:</strong> {{ $project->updated_at->format('M d, Y') }}</p>
                @if($project->screenshot_url)
                    <div class="mt-3">
                        <strong>Current Screenshot:</strong>
                        <img src="{{ $project->screenshot_url }}" class="img-fluid mt-2 rounded" alt="Project Screenshot">
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
