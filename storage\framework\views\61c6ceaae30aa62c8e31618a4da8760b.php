<!-- Dashboard Sidebar -->
<aside class="dashboard-sidebar">
    <!-- Sidebar Header -->
    <div class="dashboard-sidebar-header">
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="dashboard-logo">
            <div class="dashboard-logo-icon">
                <i class="fas fa-code"></i>
            </div>
            <div>
                <div style="font-size: 1rem; font-weight: 700; line-height: 1;">Admin Panel</div>
                <div style="font-size: 0.75rem; color: var(--dashboard-gray-500); font-weight: 500;">Portfolio CMS</div>
            </div>
        </a>
    </div>
    
    <!-- Navigation -->
    <nav class="dashboard-nav">
        <!-- Main Navigation -->
        <div class="dashboard-nav-section">
            <div class="dashboard-nav-title">Main</div>
            <ul class="dashboard-nav-list">
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" 
                       class="dashboard-nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Content Management -->
        <div class="dashboard-nav-section">
            <div class="dashboard-nav-title">Content</div>
            <ul class="dashboard-nav-list">
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('admin.projects.index')); ?>" 
                       class="dashboard-nav-link <?php echo e(request()->routeIs('admin.projects.*') ? 'active' : ''); ?>">
                        <i class="fas fa-code"></i>
                        Projects
                        
                        
                    </a>
                </li>
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('admin.messages.index')); ?>" 
                       class="dashboard-nav-link <?php echo e(request()->routeIs('admin.messages.*') ? 'active' : ''); ?>">
                        <i class="fas fa-envelope"></i>
                        Messages
                        
                        
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Website -->
        <div class="dashboard-nav-section">
            <div class="dashboard-nav-title">Website</div>
            <ul class="dashboard-nav-list">
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('home')); ?>" 
                       class="dashboard-nav-link" 
                       target="_blank"
                       title="View Website">
                        <i class="fas fa-external-link-alt"></i>
                        View Website
                    </a>
                </li>
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('contact')); ?>" 
                       class="dashboard-nav-link" 
                       target="_blank"
                       title="Contact Page">
                        <i class="fas fa-envelope-open"></i>
                        Contact Page
                    </a>
                </li>
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('projects')); ?>" 
                       class="dashboard-nav-link" 
                       target="_blank"
                       title="Projects Page">
                        <i class="fas fa-briefcase"></i>
                        Projects Page
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Account -->
        <div class="dashboard-nav-section">
            <div class="dashboard-nav-title">Account</div>
            <ul class="dashboard-nav-list">
                <li class="dashboard-nav-item">
                    <a href="<?php echo e(route('profile.edit')); ?>" 
                       class="dashboard-nav-link <?php echo e(request()->routeIs('profile.*') ? 'active' : ''); ?>">
                        <i class="fas fa-user-cog"></i>
                        Profile Settings
                    </a>
                </li>
                <li class="dashboard-nav-item">
                    <form method="POST" action="<?php echo e(route('logout')); ?>" style="margin: 0;">
                        <?php echo csrf_field(); ?>
                        <button type="submit" 
                                class="dashboard-nav-link" 
                                style="width: 100%; text-align: left; background: none; border: none; cursor: pointer;"
                                onclick="return confirm('Are you sure you want to logout?')">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Sidebar Footer -->
    <div style="margin-top: auto; padding: 1.5rem; border-top: 1px solid var(--dashboard-gray-200); background: var(--dashboard-gray-50);">
        <div style="display: flex; align-items: center; gap: 0.75rem;">
            <div style="width: 40px; height: 40px; background: var(--dashboard-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo e(substr(auth()->user()->name ?? 'U', 0, 1)); ?>

            </div>
            <div>
                <div style="font-weight: 600; font-size: 0.875rem; color: var(--dashboard-gray-900); line-height: 1;">
                    <?php echo e(auth()->user()->name ?? 'Admin User'); ?>

                </div>
                <div style="font-size: 0.75rem; color: var(--dashboard-gray-500); line-height: 1;">
                    Administrator
                </div>
            </div>
        </div>
        
        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--dashboard-gray-200);">
            <div style="font-size: 0.75rem; color: var(--dashboard-gray-500); text-align: center;">
                Portfolio CMS v1.0
            </div>
        </div>
    </div>
</aside>
<?php /**PATH D:\laravel-porfolio\resources\views/dashboard/partials/sidebar.blade.php ENDPATH**/ ?>