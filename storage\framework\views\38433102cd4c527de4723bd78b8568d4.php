<?php $__env->startSection('title', 'View Project'); ?>
<?php $__env->startSection('page-title', 'Project Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><?php echo e($project->title); ?></h2>
    <div class="btn-group">
        <a href="<?php echo e(route('admin.projects.edit', $project)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>Edit
        </a>
        <a href="<?php echo e(route('admin.projects.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Projects
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title">Description</h5>
                <p class="card-text"><?php echo e($project->description); ?></p>
                
                <h5 class="card-title mt-4">Technology Stack</h5>
                <p class="card-text">
                    <?php $__currentLoopData = explode(',', $project->tech_stack); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span class="badge bg-primary me-1"><?php echo e(trim($tech)); ?></span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </p>

                <?php if($project->github_url): ?>
                    <h5 class="card-title mt-4">GitHub Repository</h5>
                    <p class="card-text">
                        <a href="<?php echo e($project->github_url); ?>" target="_blank" class="btn btn-outline-dark">
                            <i class="fab fa-github me-2"></i>View on GitHub
                        </a>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h6 class="card-title mb-0">Project Information</h6>
            </div>
            <div class="card-body">
                <p><strong>Created:</strong><br><?php echo e($project->created_at->format('M d, Y \a\t g:i A')); ?></p>
                <p><strong>Last Updated:</strong><br><?php echo e($project->updated_at->format('M d, Y \a\t g:i A')); ?></p>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.projects.edit', $project)); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Project
                    </a>
                    <form action="<?php echo e(route('admin.projects.destroy', $project)); ?>" method="POST" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-outline-danger w-100" 
                                onclick="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                            <i class="fas fa-trash me-2"></i>Delete Project
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <?php if($project->image_path): ?>
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">Project Image</h6>
                </div>
                <div class="card-body p-0">
                    <img src="<?php echo e(asset('storage/' . $project->image_path)); ?>" class="img-fluid" alt="<?php echo e($project->title); ?> Image">
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel-porfolio\resources\views/admin/projects/show.blade.php ENDPATH**/ ?>