# 🚀 Deploy Your Portfolio NOW - Step by Step

## 📋 **Quick Deployment Checklist**

### **Step 1: Deploy to Vercel (5 minutes)**

1. **Open Vercel**: https://vercel.com
2. **Sign in with GitHub**
3. **Click "Add New..." → "Project"**
4. **Import Repository**: `umerfarooque00786/Umer-Portfolio`
5. **Configure**:
   - Project Name: `umer-portfolio`
   - Framework: `Other`
   - Build Command: `npm run build`
6. **Click "Deploy"**

### **Step 2: Set Up Database (3 minutes)**

#### **Option A: PlanetScale (Free)**
1. **Go to**: https://planetscale.com
2. **Sign up with GitHub**
3. **Create database**: `umer-portfolio`
4. **Get connection string**

#### **Option B: Railway (Free)**
1. **Go to**: https://railway.app
2. **Create MySQL database**
3. **Get connection details**

### **Step 3: Add Environment Variables (5 minutes)**

In Vercel dashboard → Settings → Environment Variables:

**Required Variables:**
```
APP_NAME = Umer Farooque Portfolio
APP_ENV = production
APP_KEY = base64:4j87ZAb0006HAMzZpaVvQ33uK4RWEUyNdHwRHnePkU8=
APP_DEBUG = false
APP_URL = https://your-project.vercel.app
```

**Database (use your database details):**
```
DB_CONNECTION = mysql
DB_HOST = your-database-host
DB_PORT = 3306
DB_DATABASE = your-database-name
DB_USERNAME = your-username
DB_PASSWORD = your-password
```

**System Variables:**
```
SESSION_DRIVER = cookie
CACHE_DRIVER = array
LOG_CHANNEL = stderr
FILESYSTEM_DISK = local
QUEUE_CONNECTION = sync
```

**Email Variables:**
```
MAIL_MAILER = log
MAIL_FROM_ADDRESS = <EMAIL>
MAIL_FROM_NAME = Umer Farooque Portfolio
MAIL_ADMIN_EMAIL = <EMAIL>
```

### **Step 4: Redeploy (1 minute)**

1. **Go to Vercel dashboard**
2. **Click "Redeploy"** (to apply environment variables)
3. **Wait for deployment**

### **Step 5: Set Up Database Tables**

After deployment, you need to create database tables. Here are 3 options:

#### **Option A: Use Database Client**
1. **Connect to your database** using phpMyAdmin or similar
2. **Run these SQL commands**:

```sql
-- Create users table
CREATE TABLE users (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    email varchar(255) NOT NULL,
    email_verified_at timestamp NULL DEFAULT NULL,
    password varchar(255) NOT NULL,
    role varchar(255) DEFAULT 'user',
    remember_token varchar(100) DEFAULT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY users_email_unique (email)
);

-- Create projects table
CREATE TABLE projects (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    title varchar(255) NOT NULL,
    description text NOT NULL,
    technologies varchar(255) NOT NULL,
    github_link varchar(255) DEFAULT NULL,
    live_link varchar(255) DEFAULT NULL,
    images json DEFAULT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

-- Create messages table
CREATE TABLE messages (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    email varchar(255) NOT NULL,
    subject varchar(255) NOT NULL,
    message text NOT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

-- Create cvs table
CREATE TABLE cvs (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    title varchar(255) NOT NULL,
    file_path varchar(255) NOT NULL,
    is_active tinyint(1) NOT NULL DEFAULT '0',
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

-- Create sessions table
CREATE TABLE sessions (
    id varchar(255) NOT NULL,
    user_id bigint unsigned DEFAULT NULL,
    ip_address varchar(45) DEFAULT NULL,
    user_agent text,
    payload longtext NOT NULL,
    last_activity int NOT NULL,
    PRIMARY KEY (id),
    KEY sessions_user_id_index (user_id),
    KEY sessions_last_activity_index (last_activity)
);

-- Create admin user
INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) 
VALUES ('Admin', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NOW(), NOW(), NOW());
```

#### **Option B: Use Vercel CLI** (if you have it installed)
```bash
vercel env pull .env.production
php artisan migrate --force
```

#### **Option C: Manual Setup**
1. **Download database client** (like phpMyAdmin)
2. **Connect to your database**
3. **Import the SQL above**

### **Step 6: Test Your Site**

1. **Visit your Vercel URL**
2. **Test all pages**:
   - Home page
   - About page
   - Projects page
   - Contact page
3. **Test admin panel**:
   - Go to `/admin`
   - Login: `<EMAIL>` / `nmdp7788`

## 🎉 **Your Site is Live!**

After following these steps, your portfolio will be live at:
`https://your-project-name.vercel.app`

## 🚨 **Common Issues & Solutions**

1. **500 Error**: Check environment variables in Vercel
2. **Database Error**: Verify database connection details
3. **Admin Login Not Working**: Make sure admin user is created in database
4. **Images Not Loading**: Check if storage folder exists

## 📞 **Need Help?**

If you get stuck:
1. Check Vercel deployment logs
2. Verify all environment variables
3. Test database connection
4. Make sure all tables are created

---

**Total Time: ~15 minutes** ⏱️
