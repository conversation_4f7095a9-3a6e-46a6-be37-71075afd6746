/* Website Components CSS */

/* Contact Form Styles */
.contact-hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.contact-form-card {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Contact Info Cards */
.contact-info-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.contact-info-card h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.contact-info-card p {
    color: #6c757d;
    margin-bottom: 1rem;
}

/* Accordion Styles */
.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.accordion-button {
    background: white;
    border: none;
    font-weight: 600;
    color: #333;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    padding: 1.5rem;
    background: #f8f9fa;
    color: #6c757d;
    line-height: 1.6;
}

/* About Page Styles */
.about-hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 80px 0;
}

.about-content {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    margin-top: -50px;
    position: relative;
    z-index: 2;
}

.profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.timeline-item {
    position: relative;
    padding-left: 80px;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: 20px;
    top: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.timeline-content {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-date {
    color: #667eea;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.timeline-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0.5rem 0;
    color: #333;
}

.timeline-company {
    color: #6c757d;
    font-style: italic;
    margin-bottom: 0.75rem;
}

.timeline-description {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Skill Progress Bars */
.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.skill-name span {
    font-weight: 600;
    color: #333;
}

.skill-percentage {
    color: #667eea;
    font-weight: 600;
}

.skill-progress {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

/* CV Download Section */
.cv-download {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    margin: 3rem 0;
}

.cv-download h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cv-download p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-light {
    background: white;
    color: #667eea;
    border: none;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    color: #667eea;
}

/* Projects Page Styles */
.projects-hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.project-filter {
    margin-bottom: 3rem;
    text-align: center;
}

.filter-btn {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;
    padding: 8px 20px;
    border-radius: 25px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

/* Swiper Styles */
.project-swiper {
    border-radius: 10px;
    overflow: hidden;
}

.project-swiper .swiper-slide img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.swiper-pagination-bullet {
    background: #667eea;
}

.swiper-button-next,
.swiper-button-prev {
    color: #667eea;
}

/* Project Modal */
.modal-content {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.modal-body {
    padding: 2rem;
}

.project-details {
    margin-bottom: 1.5rem;
}

.project-details h6 {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-details p {
    color: #6c757d;
    margin: 0;
}

/* Pagination */
.pagination {
    justify-content: center;
    margin-top: 3rem;
}

.page-link {
    border: none;
    color: #667eea;
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.page-link:hover,
.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-form-card,
    .about-content {
        padding: 2rem;
        margin-top: -30px;
    }
    
    .timeline::before {
        left: 15px;
    }
    
    .timeline-item {
        padding-left: 50px;
    }
    
    .timeline-marker {
        left: 5px;
    }
    
    .cv-download {
        padding: 2rem;
    }
    
    .cv-download h3 {
        font-size: 1.5rem;
    }
}
