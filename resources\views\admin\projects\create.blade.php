@extends('layouts.admin')

@section('title', 'Add New Project')
@section('page-title', 'Add New Project')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Project Details</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.projects.store') }}" method="POST">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Project Title *</label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title') }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="5" required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="tech_stack" class="form-label">Tech Stack *</label>
                        <input type="text" class="form-control @error('tech_stack') is-invalid @enderror" 
                               id="tech_stack" name="tech_stack" value="{{ old('tech_stack') }}" 
                               placeholder="e.g., Laravel, PHP, JavaScript, Bootstrap" required>
                        @error('tech_stack')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="github_url" class="form-label">GitHub URL</label>
                        <input type="url" class="form-control @error('github_url') is-invalid @enderror" 
                               id="github_url" name="github_url" value="{{ old('github_url') }}" 
                               placeholder="https://github.com/username/repository">
                        @error('github_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="screenshot_url" class="form-label">Screenshot URL</label>
                        <input type="url" class="form-control @error('screenshot_url') is-invalid @enderror" 
                               id="screenshot_url" name="screenshot_url" value="{{ old('screenshot_url') }}" 
                               placeholder="https://example.com/screenshot.jpg">
                        @error('screenshot_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Project
                        </button>
                        <a href="{{ route('admin.projects.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h6 class="card-title mb-0">Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <small>Use clear, descriptive project titles</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <small>Include key technologies in the tech stack</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <small>Add GitHub links to showcase your code</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <small>Screenshots help visitors visualize your work</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
