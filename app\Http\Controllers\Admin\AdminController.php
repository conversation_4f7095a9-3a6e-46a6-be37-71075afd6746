<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Message;

class AdminController extends Controller
{
    public function dashboard()
    {
        // Temporary: Mock data for testing without database
        $projectsCount = 0;
        $messagesCount = 0;
        $recentMessages = collect([]);

        return view('dashboard.dashboard', compact('projectsCount', 'messagesCount', 'recentMessages'));
    }
}
