<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Message;
use App\Models\Project;
use App\Models\Message;

class AdminController extends Controller
{
    public function dashboard()
    {
        $totalProjects = Project::count();
        $totalMessages = Message::count();
        $recentProjects = Project::latest()->take(5)->get();
        $recentMessages = Message::latest()->take(5)->get();

        return view('dashboard', compact('totalProjects', 'totalMessages', 'recentProjects', 'recentMessages'));
    }
}
