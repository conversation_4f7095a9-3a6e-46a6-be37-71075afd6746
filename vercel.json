{"version": 2, "functions": {"api/index.php": {"runtime": "vercel-php@0.6.0"}}, "routes": [{"src": "/(.*)", "dest": "/api/index.php"}], "env": {"APP_ENV": "production", "APP_DEBUG": "false", "APP_CONFIG_CACHE": "/tmp", "APP_EVENTS_CACHE": "/tmp", "APP_PACKAGES_CACHE": "/tmp", "APP_ROUTES_CACHE": "/tmp", "APP_SERVICES_CACHE": "/tmp", "VIEW_COMPILED_PATH": "/tmp", "CACHE_DRIVER": "array", "LOG_CHANNEL": "stderr", "SESSION_DRIVER": "cookie", "FILESYSTEM_DISK": "local", "QUEUE_CONNECTION": "sync"}, "build": {"env": {"COMPOSER_MIRROR_PATH_REPOS": "1"}}}