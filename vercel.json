{"version": 2, "functions": {"api/index.php": {"runtime": "vercel-php@0.7.0", "maxDuration": 30}}, "routes": [{"src": "/build/(.*)", "dest": "/public/build/$1"}, {"src": "/(css|js|images|fonts|favicon\\.ico|robots\\.txt)", "dest": "/public/$1"}, {"src": "/(.*)", "dest": "/api/index.php"}], "env": {"APP_ENV": "production", "APP_DEBUG": "false", "CACHE_DRIVER": "array", "LOG_CHANNEL": "stderr", "SESSION_DRIVER": "cookie", "QUEUE_CONNECTION": "sync"}, "buildCommand": "npm run build", "outputDirectory": "public", "installCommand": "npm install"}